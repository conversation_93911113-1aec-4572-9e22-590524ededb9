import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BioData } from "./BioDataStep";
import { Badge } from "../ui/badge";
import { MapPin, User, CreditCard, Loader2 } from "lucide-react";
import religionService from "@/lib/services/religionService";
import { PackageDetails, LocationSearchResult, PurchaseResult } from "@/types";
import { withErrorHandling, ErrorHandler } from "@/lib/errorHandler";
import { purchaseClientService } from "@/lib/services/purchaseClientService";

interface ReviewDetailsStepProps {
  packageDetails: PackageDetails;
  bioData: BioData;
  selectedLocation: LocationSearchResult;
  paymentToken: string;
  onSubmitComplete: (result: PurchaseResult) => void;
  onReset?: () => void;
}

export default function ReviewDetailsStep({
  packageDetails,
  bioData,
  selectedLocation,
  paymentToken,
  onSubmitComplete,
  onReset
}: ReviewDetailsStepProps) {
  const [religionName, setReligionName] = useState<string>("Tidak diketahui");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showEvidenceModal, setShowEvidenceModal] = useState(false);

  // Load religion name when component mounts
  useEffect(() => {
    const loadReligionName = async () => {
      if (bioData?.religionId && typeof bioData.religionId === 'string') {
        const religion = await withErrorHandling(
          () => religionService.getReligionById(bioData.religionId as string),
          'Load Religion Name'
        );
        setReligionName(religion?.name || "Tidak diketahui");
      } else {
        setReligionName("Tidak diketahui");
      }
    };
    loadReligionName();
  }, [bioData?.religionId]);

  const formatPrice = (price: number) => {
    if (price === 0) return "Gratis";
    return new Intl.NumberFormat('id-ID', { 
      style: 'currency', 
      currency: 'IDR',
      maximumFractionDigits: 0
    }).format(price);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Submit purchase data to API
      const result = await purchaseClientService.submitPurchase({
        bioData,
        selectedLocation,
        packageDetails,
        paymentToken,
      });

      if (result.success) {
        ErrorHandler.handleSuccess('Memorial digital berhasil dibuat!');
        onSubmitComplete(result.data);
      } else {
        ErrorHandler.handle('Gagal membuat memorial digital', 'Submit Memorial');
      }

    } catch (error) {
      ErrorHandler.handle(error, 'Submit Memorial');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-semibold mb-2">Review Data Memorial</h2>
        <p className="text-memorial-300">
          Periksa kembali semua data sebelum membuat memorial digital
        </p>
      </div>

      {/* Package Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Paket yang Dipilih
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-medium text-lg">{packageDetails.name}</h3>
              <p className="text-memorial-400 text-sm mb-2">Masa aktif: {packageDetails.duration}</p>
              <div className="space-y-1">
                {packageDetails.features.map((feature, index) => (
                  <div key={index} className="flex items-center text-sm">
                    <span className="text-candle-500 mr-2">✓</span>
                    <span className="text-memorial-300">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-candle-400">{formatPrice(packageDetails.price)}</p>
              <Badge variant="outline" className="mt-1">
                {packageDetails.duration}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bio Data */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Data Almarhum/Almarhumah
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Photos */}
          <div>
            <h4 className="text-memorial-200 text-sm font-medium mb-3">Foto Memorial</h4>
            <div className="flex gap-3 flex-wrap">
              {bioData.images && bioData.images.length > 0 ? (
                bioData.images.map((image, index) => (
                  <div key={index} className="w-24 h-32 sm:w-32 sm:h-40 bg-memorial-700 rounded-md overflow-hidden">
                    <img
                      src={URL.createObjectURL(image)}
                      alt={`Foto ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))
              ) : (
                <div className="w-24 h-32 sm:w-32 sm:h-40 bg-memorial-700 rounded-md flex items-center justify-center">
                  <span className="text-memorial-400 text-xs">Tidak ada foto</span>
                </div>
              )}
            </div>
          </div>

          {/* Bio Data Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-memorial-400 text-sm">Nama Lengkap</p>
              <p className="text-memorial-50 font-medium">{bioData.name}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Tempat Lahir</p>
              <p className="text-memorial-50">{bioData.birthPlace}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Tanggal Lahir</p>
              <p className="text-memorial-50">{bioData.birthDate}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Tanggal Meninggal</p>
              <p className="text-memorial-50">{bioData.deathDate}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Agama</p>
              <p className="text-memorial-50">{religionName}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Diajukan Oleh</p>
              <p className="text-memorial-50">{bioData.submittedBy}</p>
            </div>
          </div>

          {/* Life Story */}
          {bioData.description && (
            <div>
              <p className="text-memorial-400 text-sm mb-2">Kisah Hidup</p>
              <p className="text-memorial-50 text-sm leading-relaxed bg-memorial-800 p-3 rounded">
                {bioData.description}
              </p>
            </div>
          )}

          {/* Evidence */}
            <div>
            <p className="text-memorial-400 text-sm mb-2">Bukti Kematian</p>
            <div className="flex flex-col sm:flex-row sm:items-center gap-3">
              <p className="text-memorial-50 font-medium min-w-[120px]">{bioData.evidenceName}</p>
              {bioData.evidenceImage && (
              <button
                type="button"
                className="w-16 h-20 bg-memorial-700 rounded overflow-hidden border-2 border-memorial-600 hover:border-candle-500 focus:outline-none flex items-center justify-center"
                onClick={() => setShowEvidenceModal(true)}
                aria-label="Lihat bukti kematian"
              >
                <div className="relative w-full h-0 pb-[125%]"> {/* 4:5 aspect ratio */}
                <img
                  src={URL.createObjectURL(bioData.evidenceImage)}
                  alt="Bukti kematian"
                  className="absolute top-0 left-0 w-full h-full object-cover"
                />
                </div>
              </button>
              )}
            </div>
          </div>

          {/* Modal Evidence Pop Up */}
          {showEvidenceModal && bioData.evidenceImage && (
            <div
              className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm"
              onClick={() => setShowEvidenceModal(false)}
            >
              <div
                className="relative max-w-full max-h-full p-4"
                onClick={e => e.stopPropagation()}
              >
                <button
                  className="absolute top-2 right-2 bg-memorial-900/80 rounded-full p-1 text-white hover:bg-memorial-800 focus:outline-none"
                  onClick={() => setShowEvidenceModal(false)}
                  aria-label="Tutup"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
                <img
                  src={URL.createObjectURL(bioData.evidenceImage)}
                  alt="Bukti kematian"
                  className="max-h-[80vh] max-w-[90vw] rounded shadow-lg border-2 border-candle-500 bg-white"
                />
              </div>
            </div>
          )}

        </CardContent>
      </Card>

      {/* Location */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Lokasi Pemakaman
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p className="text-memorial-50 font-medium">{selectedLocation.nama_lengkap}</p>
            <p className="text-memorial-300 text-sm">{selectedLocation.formatted_address}</p>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-memorial-400">Provinsi: </span>
                <span className="text-memorial-50">{selectedLocation.provinsi}</span>
              </div>
              <div>
                <span className="text-memorial-400">Kota: </span>
                <span className="text-memorial-50">{selectedLocation.kabupaten_kota}</span>
              </div>
              <div>
                <span className="text-memorial-400">Kecamatan: </span>
                <span className="text-memorial-50">{selectedLocation.kecamatan}</span>
              </div>
              <div>
                <span className="text-memorial-400">Kelurahan: </span>
                <span className="text-memorial-50">{selectedLocation.kelurahan}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between pt-4">
        {onReset && (
          <Button
            type="button"
            variant="outline"
            onClick={onReset}
            disabled={isSubmitting}
            className="border-memorial-600 text-memorial-300 hover:bg-memorial-800"
          >
            Edit Data
          </Button>
        )}
        
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="bg-candle-500 hover:bg-candle-600 text-memorial-950 sm:ml-auto px-8"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Membuat Memorial...
            </>
          ) : (
            'Buat Memorial Digital'
          )}
        </Button>
      </div>
    </div>
  );
}
