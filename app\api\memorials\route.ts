import { NextRequest, NextResponse } from 'next/server';
import { memorialService } from '@/lib/services/memorialService';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Parse query parameters for pagination
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    // Use service to get memorials with pagination
    const result = await memorialService.getAll({
      page,
      limit,
      search: search || undefined,
    });

    // Return service response directly
    return NextResponse.json(result);

  } catch (error) {
    console.error('Error fetching memorials:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch memorials',
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}


