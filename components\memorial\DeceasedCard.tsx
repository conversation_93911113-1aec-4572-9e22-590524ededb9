import { Deceased } from "@/data/deceased";
import { useIsMobile } from "@/hooks/useIsMobile";
import { MemorialImageGallery, getMemorialImageLayout } from "./MemorialImageGallery";
import { MemorialBioSection } from "./MemorialBioSection";
import { MemorialLifeStory } from "./MemorialLifeStory";

interface DeceasedCardProps {
  deceased: Deceased;
}

export default function DeceasedCard({ deceased }: DeceasedCardProps) {
  const isMobile = useIsMobile();
  const hasTwoImages = deceased.images.length > 1;
  
  return (
    <div className="bg-memorial-900 border border-memorial-800 rounded-lg overflow-hidden mb-8">
      {/* Header */}
      <div className="p-4 bg-memorial-800 flex justify-between items-center">
        <h3 className="text-xl font-semibold text-memorial-50">{deceased.name}</h3>
        <div className="text-memorial-300">
          {deceased.birthYear} - {deceased.deathYear}
        </div>
      </div>
      
      {/* Images and Bio */}
      <div className="p-4">
        {isMobile && hasTwoImages ? (
          // Mobile with carousel: Bio below carousel
          <div className="w-full">
            <MemorialImageGallery
              images={deceased.images}
              name={deceased.name}
              isMobile={isMobile}
            />
            <div className="mt-4">
              <MemorialBioSection
                deceased={deceased}
              />
            </div>
          </div>
        ) : (
          // Desktop layout
          (() => {
            const gallery = getMemorialImageLayout({
              images: deceased.images,
              name: deceased.name,
              isMobile
            });

            if (hasTwoImages && gallery.rightImage) {
              // Two images: Left Image - Bio - Right Image
              return (
                <div className="flex flex-col md:flex-row gap-6">
                  {/* Left Image */}
                  <div className="md:w-1/4">
                    {gallery.leftImage}
                  </div>

                  {/* Bio Section in the middle */}
                  <div className="md:w-2/4">
                    <MemorialBioSection
                      deceased={deceased}
                    />
                  </div>

                  {/* Right Image */}
                  <div className="md:w-1/4">
                    {gallery.rightImage}
                  </div>
                </div>
              );
            } else {
              // Single image: Image - Bio
              return (
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="md:w-1/4">
                    {gallery.leftImage}
                  </div>
                  <div className="md:w-3/4">
                    <MemorialBioSection
                      deceased={deceased}
                    />
                  </div>
                </div>
              );
            }
          })()
        )}
      </div>
      
      {/* Life Story */}
      <MemorialLifeStory description={deceased.description} />
    </div>
  );
}
