import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select } from "@/components/ui/select";
import { Calendar, Upload } from "lucide-react";
import { Label } from "@radix-ui/react-label";
import { ErrorHandler, withErrorHandling } from "@/lib/errorHandler";
import religionService from "@/lib/services/religionService";
import { useReligionStore } from "@/store/useReligionStore";

export interface BioData {
  name: string;
  birthPlace: string;
  birthDate: string;
  deathDate: string;
  religionId: string | null;  // UUID string or null
  submittedBy: string;
  description: string;  // Changed from lifeStory to match database schema
  images: File[];  // Back to File[] for upload
  evidenceName: string;
  evidenceImage: File | null;  // Back to File for upload
  locationId?: string;  // Fixed: Always string UUID, not number
}

export interface BioDataStepProps {
  initialData?: BioData;
  onProceedToLocation: (bioData: BioData) => void;
  onReset?: () => void;
  selectedPackage?: string; // Add package info to determine photo limit
}

export default function BioDataStep({ initialData, onProceedToLocation, onReset, selectedPackage }: BioDataStepProps) {
  // Get religion from store
  const { selectedReligion } = useReligionStore();

  // Determine max images based on package
  const getMaxImages = () => {
    if (selectedPackage === 'free') return 1;
    return 2; // standard and premium
  };

  const maxImages = getMaxImages();

  // State untuk data agama (declare early to avoid reference errors)
  const [religions, setReligions] = useState<{id: string, slug: string, name: string}[]>([]);

  const [bioData, setBioData] = useState<BioData>(
    initialData || {
      name: "",
      birthPlace: "",
      birthDate: "",
      deathDate: "",
      religionId: null,  // Use null instead of 0
      submittedBy: "",
      description: "",
      images: [],  // Will be filled in upload step
      evidenceName: "",
      evidenceImage: null  // Will be filled in upload step
    }
  );

  // Reset bioData when initialData changes (e.g., when store is reset)
  useEffect(() => {

    const defaultData = {
      name: "",
      birthPlace: "",
      birthDate: "",
      deathDate: "",
      religionId: null,  // Use null instead of 0
      submittedBy: "",
      description: "",
      images: [],  // Will be filled in upload step
      evidenceName: "",
      evidenceImage: null  // Will be filled in upload step
    };

    if (initialData && Object.keys(initialData).length > 0) {
      // Check if initialData has actual content (not just empty strings)
      const hasContent = Object.values(initialData).some(value => {
        if (Array.isArray(value)) return value.length > 0;
        if (value === null || value === undefined) return false;
        if (typeof value === 'number') return value > 0; // Handle religionId properly
        return String(value).trim() !== "";
      });

      if (hasContent) {
        // Get religionId from religion store if not in session data
        const religionIdFromStore = selectedReligion && religions.length > 0
          ? getReligionIdByName(selectedReligion)
          : null;

        setBioData({
          ...initialData,
          // Reset file arrays since they can't be persisted
          images: [],
          evidenceImage: null,
          // Prioritize session religionId, fallback to religion store, then null
          religionId: initialData.religionId || religionIdFromStore || null
        });
      } else {
        // For empty session, try to load from religion store
        const religionIdFromStore = selectedReligion && religions.length > 0
          ? getReligionIdByName(selectedReligion)
          : null;

        setBioData({
          ...defaultData,
          religionId: religionIdFromStore || null
        });
      }
    } else {
      setBioData(defaultData);
    }
  }, [initialData, JSON.stringify(initialData), selectedReligion, religions]);

  // Helper function to convert religion name to ID
  const getReligionIdByName = (religionName: string | null): string | null => {
    if (!religionName || religions.length === 0) return null;

    // Map religion names to match the database/service data
    const religionMap: { [key: string]: string } = {
      'Islam': 'Islam',
      'Kristen': 'Kristen',
      'Katolik': 'Katolik',
      'Hindu': 'Hindu',
      'Buddha': 'Buddha',
      'Konghucu': 'Konghucu'
    };

    const mappedName = religionMap[religionName] || religionName;
    const religion = religions.find(r => r.name === mappedName);
    return religion ? religion.id : null;
  };

  // Load religions on component mount
  useEffect(() => {
    const loadReligions = async () => {
      const religionData = await withErrorHandling(
        () => religionService.getReligions(),
        'Load Religions'
      );

      if (religionData) {
        setReligions(religionData);
      } else {
        // Fallback to static data
        setReligions([
          { id: "cebc1d27-dad8-4029-8406-645d940a0a2a", slug: "islam", name: "Islam" },
          { id: "0a0f945b-80df-4bcf-ae23-1f9903a3ed1c", slug: "kristen", name: "Kristen" },
          { id: "20610682-e6dd-498c-977b-883d2d35d368", slug: "katolik", name: "Katolik" },
          { id: "d5145b6b-2b8f-4be6-8a89-60413b5969e1", slug: "hindu", name: "Hindu" },
          { id: "f9816867-fe50-412a-8f76-faa92d1e09b2", slug: "buddha", name: "Buddha" },
          { id: "ab875097-9eca-42d8-b6cb-9afcfb9d4f9c", slug: "konghucu", name: "Konghucu" },
        ]);
      }
    };
    loadReligions();
  }, []);

  // Load religionId from religion store when religions data is available
  useEffect(() => {
    if (religions.length > 0 && selectedReligion && !bioData.religionId) {
      const religionId = getReligionIdByName(selectedReligion);
      if (religionId) {
        console.log(`🔄 Loading religionId from religion-storage: ${selectedReligion} → ${religionId}`);
        setBioData(prev => ({ ...prev, religionId }));
      }
    }
  }, [religions, selectedReligion, bioData.religionId]);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setBioData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    if (name === 'religionId') {
      // Handle empty string as null, otherwise use the UUID string
      const uuidValue = value === '' ? null : value;
      setBioData(prev => ({ ...prev, [name]: uuidValue }));
    } else {
      setBioData(prev => ({ ...prev, [name]: value }));
    }
  };
  
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files);
      // Limit to maximum 2 images
      const limitedImages = [...bioData.images, ...newImages].slice(0, 2);
      setBioData(prev => ({ ...prev, images: limitedImages }));
    }
  };

  const handleEvidenceImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setBioData(prev => ({ ...prev, evidenceImage: e.target.files![0] }));
    }
  };

  const handleRemoveImage = (index: number) => {
    setBioData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const handleRemoveEvidenceImage = () => {
    setBioData(prev => ({
      ...prev,
      evidenceImage: null
    }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Detailed validation with specific error messages
      if (!bioData.name.trim()) {
        ErrorHandler.handle("Nama lengkap harus diisi", "Form Validation");
        return;
      }

      if (!bioData.birthPlace.trim()) {
        ErrorHandler.handle("Tempat lahir harus diisi", "Form Validation");
        return;
      }

      if (!bioData.birthDate) {
        ErrorHandler.handle("Tanggal lahir harus diisi", "Form Validation");
        return;
      }

      if (!bioData.deathDate) {
        ErrorHandler.handle("Tanggal wafat harus diisi", "Form Validation");
        return;
      }

      if (!bioData.religionId || bioData.religionId === null) {
        ErrorHandler.handle("Agama harus dipilih", "Form Validation");
        return;
      }

      if (!bioData.submittedBy.trim()) {
        ErrorHandler.handle("Nama pengaju harus diisi", "Form Validation");
        return;
      }

      if (!bioData.description.trim()) {
        ErrorHandler.handle("Kisah hidup harus diisi", "Form Validation");
        return;
      }

      if (!bioData.evidenceName.trim()) {
        ErrorHandler.handle("Nama bukti kematian harus diisi", "Form Validation");
        return;
      }

      if (!bioData.evidenceImage) {
        ErrorHandler.handle("Foto bukti kematian wajib diupload", "Form Validation");
        return;
      }

      // Validate birth date is before death date
      if (new Date(bioData.birthDate) >= new Date(bioData.deathDate)) {
        ErrorHandler.handle("Tanggal lahir harus sebelum tanggal wafat", "Form Validation");
        return;
      }

      onProceedToLocation(bioData);
    } catch (error) {
      ErrorHandler.handle(error, "Submit Bio Data");
    }
  };
  
  return (
    <div className="bg-memorial-900 p-4 sm:p-6 rounded-lg border border-memorial-800">
      <h2 className="text-lg sm:text-xl font-semibold text-memorial-50 mb-4 sm:mb-6">Data Almarhum/Almarhumah</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
        {/* Basic Information */}
        <div className="space-y-3 sm:space-y-4">
          <div>
            <Label htmlFor="name" className="text-sm sm:text-base text-memorial-200 mb-1 block">Nama Lengkap</Label>
            <Input
              id="name"
              name="name"
              value={bioData.name}
              onChange={handleChange}
              className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base"
              placeholder="Masukkan nama lengkap almarhum/almarhumah"
            />
          </div>
          
          <div>
            <Label htmlFor="birthPlace" className="text-sm sm:text-base text-memorial-200 mb-1 block">Tempat Lahir</Label>
            <Input
              id="birthPlace"
              name="birthPlace"
              value={bioData.birthPlace}
              onChange={handleChange}
              className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base"
              placeholder="Masukkan tempat lahir"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
            <div>
              <Label htmlFor="birthDate" className="text-sm sm:text-base text-memorial-200 mb-1 block">Tanggal Lahir</Label>
              <div className="relative">
                <Input
                  id="birthDate"
                  name="birthDate"
                  type="date"
                  value={bioData.birthDate}
                  onChange={handleChange}
                  className="bg-memorial-800 border-memorial-700 text-memorial-50 pl-10 text-sm sm:text-base"
                />
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-memorial-400" />
              </div>
            </div>
            
            <div>
              <Label htmlFor="deathDate" className="text-sm sm:text-base text-memorial-200 mb-1 block">Tanggal Meninggal</Label>
              <div className="relative">
                <Input
                  id="deathDate"
                  name="deathDate"
                  type="date"
                  value={bioData.deathDate}
                  onChange={handleChange}
                  className="bg-memorial-800 border-memorial-700 text-memorial-50 pl-10 text-sm sm:text-base"
                />
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-memorial-400" />
              </div>
            </div>
          </div>
          
          <div>
            <Label htmlFor="submittedBy" className="text-sm sm:text-base text-memorial-200 mb-1 block">Diajukan Oleh</Label>
            <Input
              id="submittedBy"
              name="submittedBy"
              value={bioData.submittedBy}
              onChange={handleChange}
              className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base"
              placeholder="Nama pengaju/keluarga"
            />
          </div>

          <div>
            <Label htmlFor="religionId" className="text-sm sm:text-base text-memorial-200 mb-1 block">Agama</Label>
            <Select
              options={religions.map(religion => ({ value: religion.id.toString(), label: religion.name }))}
              value={bioData.religionId ? bioData.religionId.toString() : ''}
              onChange={(value) => handleSelectChange('religionId', value)}
              placeholder="Pilih Agama"
              className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base"
            />
          </div>
        </div>

        {/* Photo Upload */}
        <div>
          <Label className="text-sm sm:text-base text-memorial-200 block mb-1 sm:mb-2">
            Foto (Maksimal {maxImages} foto{maxImages > 1 ? '' : ''})
          </Label>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            {/* Image preview area */}
            <div className="flex gap-3 sm:gap-4 flex-wrap">
              {bioData.images.map((image, index) => (
                <div key={index} className="relative w-24 h-32 sm:w-32 sm:h-40 bg-memorial-800 rounded-md overflow-hidden">
                  <img
                    src={URL.createObjectURL(image)}
                    alt={`Preview ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  <button
                    type="button"
                    onClick={() => handleRemoveImage(index)}
                    className="absolute top-1 right-1 bg-memorial-950/80 text-memorial-50 rounded-full p-1"
                  >
                    ✕
                  </button>
                </div>
              ))}

              {bioData.images.length < maxImages && (
                <label className="flex items-center justify-center w-24 h-32 sm:w-32 sm:h-40 bg-memorial-800 border border-dashed border-memorial-600 rounded-md cursor-pointer hover:bg-memorial-700/50 transition-colors">
                  <div className="flex flex-col items-center">
                    <Upload className="h-5 w-5 sm:h-6 sm:w-6 text-memorial-400 mb-1 sm:mb-2" />
                    <span className="text-xs sm:text-sm text-memorial-400">Upload Foto</span>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageUpload}
                  />
                </label>
              )}
            </div>
          </div>
          <p className="text-xs sm:text-sm text-memorial-400 mt-1 sm:mt-2">Format: JPG, PNG. Ukuran maksimal: 5MB per foto.</p>
        </div>

        {/* Evidence Upload */}
        <div>
          <Label htmlFor="evidenceName" className="text-sm sm:text-base text-memorial-200 mb-1 block">Nama Bukti Kematian</Label>
          <Input
            id="evidenceName"
            name="evidenceName"
            value={bioData.evidenceName}
            onChange={handleChange}
            className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base mb-3"
            placeholder="Contoh: Surat Kematian RS XYZ"
          />

          <Label className="text-sm sm:text-base text-memorial-200 block mb-1 sm:mb-2">
            Foto Bukti Kematian <span className="text-red-500">*</span>
          </Label>
          <div className="flex gap-3 sm:gap-4">
            {bioData.evidenceImage && (
              <div className="relative w-24 h-32 sm:w-32 sm:h-40 bg-memorial-800 rounded-md overflow-hidden">
                <img
                  src={URL.createObjectURL(bioData.evidenceImage)}
                  alt="Evidence Preview"
                  className="w-full h-full object-cover"
                />
                <button
                  type="button"
                  onClick={handleRemoveEvidenceImage}
                  className="absolute top-1 right-1 bg-memorial-950/80 text-memorial-50 rounded-full p-1"
                >
                  ✕
                </button>
              </div>
            )}

            {!bioData.evidenceImage && (
              <label className="flex items-center justify-center w-24 h-32 sm:w-32 sm:h-40 bg-memorial-800 border border-dashed border-memorial-600 rounded-md cursor-pointer hover:bg-memorial-700/50 transition-colors">
                <div className="flex flex-col items-center">
                  <Upload className="h-5 w-5 sm:h-6 sm:w-6 text-memorial-400 mb-1 sm:mb-2" />
                  <span className="text-xs sm:text-sm text-memorial-400">Upload Bukti</span>
                </div>
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleEvidenceImageUpload}
                />
              </label>
            )}
          </div>
          <p className="text-xs sm:text-sm text-memorial-400 mt-1 sm:mt-2">Format: JPG, PNG. Ukuran maksimal: 5MB.</p>
        </div>

        {/* Life Story */}
        <div>
          <Label htmlFor="description" className="text-sm sm:text-base text-memorial-200 mb-1 block">Kisah Hidup</Label>
          <Textarea
            id="description"
            name="description"
            value={bioData.description}
            onChange={handleChange}
            className="bg-memorial-800 border-memorial-700 text-memorial-50 min-h-[120px] sm:min-h-[150px] text-sm sm:text-base"
            placeholder="Ceritakan kisah hidup almarhum/almarhumah..."
          />
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          {onReset && (
            <Button
              type="button"
              variant="outline"
              onClick={onReset}
              className="w-full sm:w-auto border-memorial-600 text-memorial-300 hover:bg-memorial-800 hover:text-memorial-50 text-sm sm:text-base py-2 sm:py-3"
            >
              Reset Form
            </Button>
          )}
          <Button
            type="submit"
            className="flex-1 bg-candle-500 text-memorial-950 hover:bg-candle-400 text-sm sm:text-base py-2 sm:py-3"
          >
            Lanjutkan ke Pemilihan Lokasi Pemakaman
          </Button>
        </div>
      </form>
    </div>
  );
}



