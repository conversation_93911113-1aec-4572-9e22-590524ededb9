{"name": "pemakaman-digital", "version": "0.1.0", "description": "Digital memorial platform for creating and managing online memorials", "private": true, "author": "Your Name", "license": "MIT", "keywords": ["memorial", "digital", "nextjs", "typescript", "supabase"], "repository": {"type": "git", "url": "https://github.com/glenrioariesto/pemakaman-digital.git"}, "bugs": {"url": "https://github.com/glenrioariesto/pemakaman-digital/issues"}, "homepage": "https://github.com/glenrioariesto/pemakaman-digital#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "npx cross-env NODE_ENV=development next dev", "build": "next build", "start": "npx cross-env NODE_ENV=production node scripts/load-env.js && npx cross-env NODE_ENV=production next start", "start:check": "npx cross-env NODE_ENV=production node scripts/load-env.js", "lint": "next lint", "db:generate": "drizzle-kit generate:pg", "db:migrate": "npx tsx scripts/migrate.ts", "db:reset:seed": "npx tsx scripts/reset-and-seed.ts", "db:reset:seed:prod": "npx cross-env NODE_ENV=production npx tsx scripts/reset-and-seed.ts", "db:push": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio", "setup:supabase": "npx tsx scripts/setup-supabase-storage.ts", "check:env": "npx tsx scripts/check-env.ts", "check:env:prod": "npx cross-env NODE_ENV=production npx tsx scripts/check-env.ts", "update:nextjs": "npm install next@latest react@latest react-dom@latest", "clean": "rm -rf .next out dist", "postinstall": "echo 'Installation complete'"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.38.4", "@types/pg": "^8.15.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.28.5", "embla-carousel-react": "^8.0.0", "framer-motion": "^12.18.1", "lucide-react": "^0.515.0", "next": "^14.2.3", "next-themes": "^0.4.6", "pg": "^8.16.1", "postgres": "^3.4.7", "react": "^18.2.0", "react-dom": "^18.2.0", "sharp": "^0.34.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3", "zod": "^3.25.64", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20.11.30", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "drizzle-kit": "^0.19.13", "postcss": "^8.5.5", "tailwindcss": "^3.4.17", "typescript": "^5.4.2"}}