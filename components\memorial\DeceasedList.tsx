import { Deceased } from "@/data/deceased";
import DeceasedCard from "@/components/memorial/DeceasedCard";

interface DeceasedListProps {
  deceasedList: Deceased[];
}

export default function DeceasedList({ deceasedList }: DeceasedListProps) {
  return (
    <div className="space-y-8">
      {deceasedList.map((deceased) => (
        <DeceasedCard key={deceased.id} deceased={deceased} />
      ))}
    </div>
  );
}