import { Deceased } from "@/data/deceased";
import { BioItem, BioItemWithLocation } from "./BioItem";
import { formatDate, getLocationUrl } from "@/lib/utils/memorialUtils";

interface MemorialBioSectionProps {
  deceased: Deceased;
}

export function MemorialBioSection({ deceased }: MemorialBioSectionProps) {
  const locationUrl = getLocationUrl(deceased);

  return (
    <div className="w-full space-y-2 sm:space-y-3">
      <BioItem label="Nama" value={deceased.name} />
      <BioItem label="Tempat Lahir" value={deceased.birthPlace} />
      <BioItem label="Tanggal Lahir" value={formatDate(deceased.birthDate)} />
      <BioItem label="Tanggal Meninggal" value={formatDate(deceased.deathDate)} />
      <BioItemWithLocation
        label="Tempat Pemakaman"
        value={deceased.cemeteryName}
        locationUrl={locationUrl}
      />
      <BioItem label="Diajukan Ole<PERSON>" value={deceased.submittedBy} />
    </div>
  );
}
