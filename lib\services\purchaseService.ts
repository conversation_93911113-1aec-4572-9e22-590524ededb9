import { db } from '@/db';
import { memorials, locations, memorialImages, orders } from '@/db/schema';
import { PurchaseRequest } from '@/validations/purchaseSchema';
import { ApiError } from '@/utils/ApiError';
import { imageStorageService } from './imageStorageService';
import { eq } from 'drizzle-orm';
import { generateMemorialSlug, generateLocationSlug, makeSlugUnique } from '@/lib/utils/slug';

export interface PurchaseResult {
  memorialId: string;
  memorialSlug: string;
  locationId: string;
  orderId: string;
  packageDetails: {
    id: string;
    name: string;
    price: number;
    duration: string;
    features: string[];
  };
  imageUrls: string[];
  evidenceImageUrl: string | null;
  pricing: {
    packagePrice: number;
    adminFee: number;
    total: number;
  };
}

class PurchaseService {
  /**
   * Generate unique memorial slug by checking for duplicates
   */
  private async generateUniqueMemorialSlug(name: string, birthDate: string, deathDate: string): Promise<string> {
    let baseSlug = generateMemorialSlug(name, birthDate, deathDate);
    let finalSlug = baseSlug;
    let attempts = 0;
    const maxAttempts = 10;

    // Check if slug already exists
    while (attempts < maxAttempts) {
      const existingMemorial = await db.query.memorials.findFirst({
        where: (memorials, { eq }) => eq(memorials.slug, finalSlug),
        columns: { id: true }
      });

      if (!existingMemorial) {
        // Slug is unique, we can use it
        return finalSlug;
      }

      // Slug exists, generate a new one with timestamp suffix
      attempts++;
      finalSlug = makeSlugUnique(baseSlug);

      console.log(`🔄 Slug collision detected for "${baseSlug}", trying "${finalSlug}" (attempt ${attempts})`);
    }

    // If we still can't find a unique slug after max attempts, add random suffix
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    finalSlug = `${baseSlug}-${randomSuffix}`;

    console.log(`⚠️ Max attempts reached, using random suffix: "${finalSlug}"`);
    return finalSlug;
  }

  /**
   * Generate unique location slug by checking for duplicates
   */
  private async generateUniqueLocationSlug(name: string, city: string, province: string): Promise<string> {
    let baseSlug = generateLocationSlug(name, city, province);
    let finalSlug = baseSlug;
    let attempts = 0;
    const maxAttempts = 10;

    // Check if slug already exists
    while (attempts < maxAttempts) {
      const existingLocation = await db.query.locations.findFirst({
        where: (locations, { eq }) => eq(locations.slug, finalSlug),
        columns: { id: true }
      });

      if (!existingLocation) {
        // Slug is unique, we can use it
        return finalSlug;
      }

      // Slug exists, generate a new one with timestamp suffix
      attempts++;
      finalSlug = makeSlugUnique(baseSlug);

      console.log(`🔄 Location slug collision detected for "${baseSlug}", trying "${finalSlug}" (attempt ${attempts})`);
    }

    // If we still can't find a unique slug after max attempts, add random suffix
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    finalSlug = `${baseSlug}-${randomSuffix}`;

    console.log(`⚠️ Max attempts reached for location, using random suffix: "${finalSlug}"`);
    return finalSlug;
  }

  /**
   * Convert religionId from string to UUID
   */
  private async getReligionUUID(religionId: string | null | undefined): Promise<string | null> {
    if (!religionId) return null;

    // If it's already a UUID format, return as is
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(religionId)) {
      return religionId;
    }

    // Try to find religion by name or slug (for backward compatibility)
    const religion = await db.query.religions.findFirst({
      where: (religions, { or, eq, ilike }) => or(
        eq(religions.id, religionId),
        ilike(religions.name, religionId),
        eq(religions.slug, religionId)
      )
    });

    if (!religion) {
      throw new ApiError(`Religion not found: ${religionId}`, 400, 'INVALID_RELIGION');
    }

    return religion.id;
  }

  /**
   * Process a complete memorial purchase
   */
  async processPurchase(purchaseData: PurchaseRequest): Promise<PurchaseResult> {
    try {
      // Validate package
      if (!this.isValidPackage(purchaseData.packageDetails.id)) {
        throw new ApiError('Invalid package selected', 400, 'INVALID_PACKAGE');
      }

      // Start database transaction
      const result = await db.transaction(async (tx) => {
        // 0. Convert religionId to UUID
        const religionUUID = await this.getReligionUUID(purchaseData.religionId);

        // 1. Create location record with unique slug
        const locationSlug = await this.generateUniqueLocationSlug(
          purchaseData.locationData.name,
          purchaseData.locationData.city,
          purchaseData.locationData.province
        );

        const [location] = await tx.insert(locations).values({
          slug: locationSlug,
          name: purchaseData.locationData.name,
          addressDetail: purchaseData.locationData.addressDetail,
          province: purchaseData.locationData.province,
          city: purchaseData.locationData.city,
          district: purchaseData.locationData.district,
          subDistrict: purchaseData.locationData.subDistrict,
          latitude: purchaseData.locationData.latitude,
          longitude: purchaseData.locationData.longitude,
          placeId: purchaseData.locationData.placeId,
        }).returning();

        // 2. Upload evidence image if provided
        let evidenceImageUrl: string | null = null;
        if (purchaseData.evidenceImage) {
          const evidenceResult = await imageStorageService.uploadBase64Image(
            purchaseData.evidenceImage,
            `evidence_${Date.now()}`,
            'evidence',
            purchaseData.paymentToken
          );
          evidenceImageUrl = evidenceResult.publicUrl;
        }

        // 3. Create memorial record with unique slug
        const memorialSlug = await this.generateUniqueMemorialSlug(
          purchaseData.name,
          purchaseData.birthDate,
          purchaseData.deathDate
        );

        const [memorial] = await tx.insert(memorials).values({
          slug: memorialSlug,
          name: purchaseData.name,
          birthPlace: purchaseData.birthPlace,
          birthDate: purchaseData.birthDate,
          deathDate: purchaseData.deathDate,
          religionId: religionUUID,
          locationId: location.id,
          description: purchaseData.description,
          submittedBy: purchaseData.submittedBy,
          evidenceName: purchaseData.evidenceName,
          evidenceImageUrl: evidenceImageUrl,
        }).returning();

        // 4. Upload memorial images if provided
        const imageUrls: string[] = [];
        if (purchaseData.images && purchaseData.images.length > 0) {
          const uploadResults = await imageStorageService.uploadMultipleBase64Images(
            purchaseData.images,
            `memorial_${memorial.id}`,
            'memorial',
            purchaseData.paymentToken
          );

          for (let i = 0; i < uploadResults.length; i++) {
            const result = uploadResults[i];
            imageUrls.push(result.publicUrl);

            // Insert memorial image record
            await tx.insert(memorialImages).values({
              memorialId: memorial.id,
              imageUrl: result.publicUrl,
              caption: `Memorial image ${i + 1}`,
            });
          }
        }

        // 5. Calculate pricing
        const pricing = this.calculateTotalPrice(purchaseData.packageDetails.price);

        // 6. Create order record
        const [order] = await tx.insert(orders).values({
          memorialId: memorial.id,
          packageId: purchaseData.packageDetails.id,
          packageName: purchaseData.packageDetails.name,
          packagePrice: purchaseData.packageDetails.price,
          packageDuration: purchaseData.packageDetails.duration,
          packageFeatures: JSON.stringify(purchaseData.packageDetails.features),
          adminFee: pricing.adminFee,
          totalPrice: pricing.total,
          paymentMethod: purchaseData.packageDetails.price > 0 ? 'pending' : null,
          paymentStatus: purchaseData.packageDetails.price > 0 ? 'pending' : 'paid',
        }).returning();

        return {
          memorial,
          location,
          order,
          imageUrls,
          evidenceImageUrl,
          pricing,
        };
      });

      // Return structured result
      return {
        memorialId: result.memorial.id,
        memorialSlug: result.memorial.slug,
        locationId: result.location.id,
        orderId: result.order.id,
        packageDetails: purchaseData.packageDetails,
        imageUrls: result.imageUrls,
        evidenceImageUrl: result.evidenceImageUrl,
        pricing: result.pricing,
      };

    } catch (error) {
      console.error('Purchase processing error:', error);
      throw new ApiError(
        'Failed to process purchase',
        500,
        'PURCHASE_PROCESSING_ERROR'
      );
    }
  }

  /**
   * Check if package ID is valid
   */
  private isValidPackage(packageId: string): boolean {
    // Match DB schema comment: 'free', 'basic', 'premium', 'ultimate'
    // But use actual packages from PackageStep: 'free', 'standard', 'premium'
    const validPackages = ['free', 'standard', 'premium'];
    return validPackages.includes(packageId);
  }

  /**
   * Get memorial details by ID
   */
  async getMemorialById(memorialId: string) {
    try {
      const memorial = await db.query.memorials.findFirst({
        where: (memorials, { eq }) => eq(memorials.id, memorialId),
        with: {
          memorialImages: true,
        }
      });

      if (!memorial) {
        throw new ApiError('Memorial not found', 404, 'MEMORIAL_NOT_FOUND');
      }

      return memorial;

    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      console.error('Get memorial error:', error);
      throw new ApiError(
        'Failed to retrieve memorial',
        500,
        'MEMORIAL_RETRIEVAL_ERROR'
      );
    }
  }

  /**
   * Validate package details
   */
  validatePackage(packageId: string): boolean {
    return this.isValidPackage(packageId);
  }

  /**
   * Calculate total price including admin fee
   */
  calculateTotalPrice(packagePrice: number): { packagePrice: number; adminFee: number; total: number } {
    const adminFee = packagePrice === 0 ? 0 : Math.max(packagePrice * 0.05, 2500);
    const total = packagePrice + adminFee;

    return {
      packagePrice,
      adminFee,
      total,
    };
  }

  /**
   * Convert File objects to base64 for API transmission
   */
  async convertFilesToBase64(files: File[]): Promise<string[]> {
    const base64Images: string[] = [];
    
    for (const file of files) {
      const base64 = await this.fileToBase64(file);
      base64Images.push(base64);
    }
    
    return base64Images;
  }

  /**
   * Convert single File to base64
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  }
}

export const purchaseService = new PurchaseService();
