import { BioData } from '@/components/purchase/BioDataStep';
import { PackageDetails, LocationSearchResult, PurchaseResult } from '@/types';
import { apiRequest } from '@/lib/errorHandler';

export interface PurchaseData {
  bioData: BioData;
  selectedLocation: LocationSearchResult;
  packageDetails: PackageDetails;
  paymentToken: string;
}

export interface PurchaseResponse {
  success: boolean;
  message: string;
  data: PurchaseResult;
}

class PurchaseClientService {
  /**
   * Submit complete purchase data to API
   */
  async submitPurchase(purchaseData: PurchaseData): Promise<PurchaseResponse> {
    try {
      // Convert File objects to base64 strings
      const images = await this.convertFilesToBase64(purchaseData.bioData.images);
      const evidenceImage = purchaseData.bioData.evidenceImage
        ? await this.fileToBase64(purchaseData.bioData.evidenceImage)
        : null;

      // Prepare API request payload
      const payload = {
        // Bio data
        name: purchaseData.bioData.name,
        birthPlace: purchaseData.bioData.birthPlace,
        birthDate: purchaseData.bioData.birthDate,
        deathDate: purchaseData.bioData.deathDate,
        religionId: purchaseData.bioData.religionId,
        submittedBy: purchaseData.bioData.submittedBy,
        description: purchaseData.bioData.description,
        evidenceName: purchaseData.bioData.evidenceName,
        
        // Location data
        locationData: {
          name: purchaseData.selectedLocation.nama_lengkap,
          addressDetail: purchaseData.selectedLocation.alamat_detail || purchaseData.selectedLocation.formatted_address,
          province: purchaseData.selectedLocation.provinsi,
          city: purchaseData.selectedLocation.kabupaten_kota,
          district: purchaseData.selectedLocation.kecamatan,
          subDistrict: purchaseData.selectedLocation.kelurahan,
          latitude: purchaseData.selectedLocation.lokasi.latitude,
          longitude: purchaseData.selectedLocation.lokasi.longitude,
          placeId: purchaseData.selectedLocation.place_id,
        },
        
        // Package details
        packageDetails: purchaseData.packageDetails,

        // Payment authorization token
        paymentToken: purchaseData.paymentToken,

        // File uploads
        images,
        evidenceImage,
      };

      // Make API request
      const response = await apiRequest<{data: PurchaseResponse['data']}>(
        '/api/purchase',
        {
          method: 'POST',
          body: JSON.stringify(payload),
        },
        'Purchase Submission'
      );

      if (!response?.data) {
        throw new Error('Invalid response from server');
      }

      return {
        success: true,
        message: 'Purchase completed successfully',
        data: response.data,
      };

    } catch (error) {
      console.error('Purchase submission error:', error);
      throw error;
    }
  }

  /**
   * Get memorial details by ID
   */
  async getMemorialById(memorialId: string) {
    try {
      const response = await apiRequest(
        `/api/purchase?memorialId=${memorialId}`,
        {
          method: 'GET',
        },
        'Get Memorial Details'
      );

      return response;

    } catch (error) {
      console.error('Get memorial error:', error);
      throw error;
    }
  }

  /**
   * Convert File objects to base64 strings
   */
  private async convertFilesToBase64(files: File[]): Promise<string[]> {
    const base64Images: string[] = [];

    for (const file of files) {
      const base64 = await this.fileToBase64(file);
      base64Images.push(base64);
    }

    return base64Images;
  }

  /**
   * Convert single File to base64 data URL
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  }

  /**
   * Validate purchase data before submission
   */
  validatePurchaseData(purchaseData: PurchaseData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate bio data
    if (!purchaseData.bioData.name.trim()) {
      errors.push('Nama tidak boleh kosong');
    }
    if (!purchaseData.bioData.birthPlace.trim()) {
      errors.push('Tempat lahir tidak boleh kosong');
    }
    if (!purchaseData.bioData.birthDate) {
      errors.push('Tanggal lahir tidak boleh kosong');
    }
    if (!purchaseData.bioData.deathDate) {
      errors.push('Tanggal meninggal tidak boleh kosong');
    }
    // religionId is optional in DB schema, so we don't require it
    // if (!purchaseData.bioData.religionId || purchaseData.bioData.religionId === null) {
    //   errors.push('Agama harus dipilih');
    // }
    if (!purchaseData.bioData.submittedBy.trim()) {
      errors.push('Nama pengaju tidak boleh kosong');
    }
    if (!purchaseData.bioData.description.trim()) {
      errors.push('Kisah hidup tidak boleh kosong');
    }
    if (!purchaseData.bioData.evidenceName.trim()) {
      errors.push('Nama bukti kematian tidak boleh kosong');
    }
    if (!purchaseData.bioData.evidenceImage) {
      errors.push('Foto bukti kematian wajib diupload');
    }

    // Validate dates
    if (purchaseData.bioData.birthDate && purchaseData.bioData.deathDate) {
      const birthDate = new Date(purchaseData.bioData.birthDate);
      const deathDate = new Date(purchaseData.bioData.deathDate);
      
      if (birthDate >= deathDate) {
        errors.push('Tanggal lahir harus sebelum tanggal meninggal');
      }
    }

    // Validate location
    if (!purchaseData.selectedLocation) {
      errors.push('Lokasi harus dipilih');
    } else {
      if (!purchaseData.selectedLocation.lokasi?.latitude || !purchaseData.selectedLocation.lokasi?.longitude) {
        errors.push('Koordinat lokasi tidak valid');
      }
    }

    // Validate package
    if (!purchaseData.packageDetails) {
      errors.push('Paket harus dipilih');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Calculate total price including admin fee
   */
  calculateTotalPrice(packagePrice: number): { packagePrice: number; adminFee: number; total: number } {
    const adminFee = packagePrice === 0 ? 0 : Math.max(packagePrice * 0.05, 2500);
    const total = packagePrice + adminFee;

    return {
      packagePrice,
      adminFee,
      total,
    };
  }

  /**
   * Format price to Indonesian Rupiah
   */
  formatPrice(price: number): string {
    if (price === 0) return "Gratis";
    return new Intl.NumberFormat('id-ID', { 
      style: 'currency', 
      currency: 'IDR',
      maximumFractionDigits: 0
    }).format(price);
  }
}

export const purchaseClientService = new PurchaseClientService();
