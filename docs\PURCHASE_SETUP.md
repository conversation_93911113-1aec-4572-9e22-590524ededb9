w# Setup Purchase Flow dengan Drizzle ORM

Dokumentasi ini menjelaskan cara mengatur purchase flow yang terintegrasi dengan database PostgreSQL dan Supabase Storage menggunakan Drizzle ORM yang type-safe.

## 🗄️ Database Setup

### 1. Persiapan Database

Pastikan PostgreSQL sudah terinstall dan ber<PERSON>lan. Kemudian jalankan Drizzle migration:

```bash
# Reset database (hapus semua tabel)
npm run db:reset

# Fresh migration (reset + migrate)
npm run db:fresh

# Atau step by step:
# 1. Generate migration files (jika ada perubahan schema)
npm run db:generate

# 2. Jalankan migration ke database
npm run db:migrate
```

**Environment Variables**
Pastikan file `.env.local` memiliki konfigurasi database yang benar:

```env
# Environment
NODE_ENV=development

# Database Configuration (PostgreSQL Lokal untuk Development)
# Primary connection string (recommended) - URL encode password jika ada karakter khusus
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/pemakaman_digital

# Individual database variables (fallback untuk scripts)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pemakaman_digital
DB_USER=postgres
DB_PASSWORD=your_password

# Google Maps API Key
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Supabase Configuration (untuk Image Storage)
# NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

**Catatan Penting:**
- Jika password mengandung karakter khusus (seperti `.`, `@`, `#`), encode dalam DATABASE_URL
- Scripts menggunakan `dotenv` untuk load `.env.local`
- Individual variables digunakan sebagai fallback

### 2. Struktur Database

Drizzle migration akan membuat tabel berikut sesuai dengan `db/schema.ts`:

- **memorials**: Data almarhum dengan relasi ke locations dan religions
- **locations**: Data lokasi pemakaman dengan koordinat Google Maps
- **orders**: Data pemesanan dengan informasi paket dan pricing lengkap
- **memorial_images**: Galeri foto memorial dengan relasi ke memorials
- **religions**: Data agama (auto-seeded dengan 6 agama utama)

### 3. Type Safety

Semua types di-generate otomatis dari Drizzle schema:

```typescript
// db/types.ts - Auto-generated dari schema
export type Memorial = InferSelectModel<typeof memorials>;
export type NewMemorial = InferInsertModel<typeof memorials>;
export type Order = InferSelectModel<typeof orders>;
// ... dll

// types/index.ts - Re-export untuk kemudahan
export type { Memorial, Order, Location } from '@/db/types';
```

### 3. Environment Variables

Pastikan file `.env.local` memiliki konfigurasi database:

```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pemakaman_digital
DB_USER=postgres
DB_PASSWORD=your_password

# Supabase (untuk image storage)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 🖼️ Image Storage Setup

### 1. Supabase Storage

Sistem menggunakan Supabase Storage untuk menyimpan gambar:

- **Bucket**: `memorial-images`
- **Folders**: 
  - `memorial/` - Foto memorial
  - `evidence/` - Foto bukti kematian

### 2. Storage Policies

Buat policies di Supabase Dashboard:

```sql
-- Allow public read access
CREATE POLICY "Public read access" ON storage.objects
FOR SELECT USING (bucket_id = 'memorial-images');

-- Allow authenticated upload
CREATE POLICY "Authenticated upload" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'memorial-images');
```

## 🔄 Purchase Flow

### 1. Alur Proses

1. **BioData Step**: User mengisi data almarhum
2. **Location Step**: User memilih lokasi pemakaman
3. **Details Step**: User memilih paket memorial
4. **Payment Step**: Sistem menyimpan data ke database
5. **Confirmation Step**: Menampilkan hasil

### 2. Data yang Disimpan

Ketika user mencapai Payment Step, sistem akan:

1. **Insert Location**: Menyimpan data lokasi ke tabel `locations`
2. **Upload Images**: Upload foto ke Supabase Storage
3. **Insert Memorial**: Menyimpan data almarhum ke tabel `memorials`
4. **Insert Memorial Images**: Menyimpan referensi foto ke tabel `memorial_images`
5. **Insert Order**: Menyimpan data pemesanan ke tabel `orders` dengan informasi paket lengkap

### 3. Validation

Sistem menggunakan Zod schema untuk validasi:

- **Server-side**: `validations/purchaseSchema.ts`
- **Client-side**: `purchaseClientService.validatePurchaseData()`

## 🧪 Testing

### 1. Test Purchase Flow

```bash
# Test validasi dan flow
npm run test:purchase
```

### 2. Test Database Connection

```bash
# Test koneksi dan jalankan migration
npm run db:migrate
```

### 3. Manual Testing

1. Jalankan migration: `npm run db:migrate`
2. Start aplikasi: `npm run dev`
3. Buka browser ke `http://localhost:3000/purchase`
4. Isi semua step sampai Payment
5. Klik "Aktivasi Memorial Gratis" atau "Bayar Sekarang"
6. Periksa database untuk memastikan data tersimpan

## 📊 Monitoring

### 1. Database Queries

Untuk melihat data yang tersimpan:

```sql
-- Lihat memorial terbaru dengan orders
SELECT m.*, l.name as location_name, o.package_name, o.total_price, o.payment_status
FROM memorials m
LEFT JOIN locations l ON m.location_id = l.id
LEFT JOIN orders o ON m.id = o.memorial_id
ORDER BY m.created_at DESC
LIMIT 10;

-- Lihat statistik orders berdasarkan package
SELECT o.package_id, o.package_name, COUNT(o.id) as total_orders, SUM(o.total_price) as total_revenue
FROM orders o
GROUP BY o.package_id, o.package_name
ORDER BY total_orders DESC;

-- Lihat orders dengan status pembayaran
SELECT payment_status, COUNT(*) as count, SUM(total_price) as total_amount
FROM orders
GROUP BY payment_status;
```

### 2. Error Handling

Sistem menggunakan:

- **Server**: `ApiError` class untuk error handling
- **Client**: `ErrorHandler` middleware untuk toast notifications
- **Database**: Transaction rollback pada error

## 🔧 Troubleshooting

### 1. Database Connection Error

```bash
# Periksa status PostgreSQL
sudo systemctl status postgresql

# Restart jika perlu
sudo systemctl restart postgresql
```

### 2. Schema Mismatch Error

Jika mendapat error terkait kolom yang tidak ada atau tipe data yang salah:

```bash
# Generate ulang migration
npm run db:generate

# Jalankan migration
npm run db:migrate
```

Drizzle akan otomatis mendeteksi perbedaan schema dan membuat migration yang sesuai.

### 2. Migration Error

```bash
# Reset database jika perlu
psql -U postgres -c "DROP DATABASE IF EXISTS pemakaman_digital;"
psql -U postgres -c "CREATE DATABASE pemakaman_digital;"
npm run db:migrate
```

### 3. Supabase Storage Error

- Periksa bucket `memorial-images` sudah dibuat
- Pastikan policies sudah diset dengan benar
- Cek environment variables Supabase

### 4. Image Upload Error

- Pastikan file size < 5MB
- Format yang didukung: JPEG, PNG, WebP
- Periksa network connection ke Supabase

## 📝 Development Notes

### 1. Schema Changes

Jika perlu mengubah schema:

1. Update `db/schema.ts`
2. Buat migration script baru
3. Test di development environment
4. Deploy ke production

### 2. Adding New Package

Untuk menambah paket baru, update kode di:
- `lib/services/purchaseService.ts` - method `isValidPackage()`
- `components/purchase/DetailsStep.tsx` - daftar paket yang tersedia

Tidak perlu insert ke database karena informasi paket disimpan langsung di order.

### 3. Performance Optimization

- Index sudah dibuat untuk query yang sering digunakan
- Image compression di client-side sebelum upload
- Database connection pooling
- Lazy loading untuk images

## 🚀 Deployment

### 1. Production Database

- Gunakan managed PostgreSQL (seperti Supabase, Railway, atau AWS RDS)
- Set connection pooling
- Enable SSL connection

### 2. Environment Variables

Pastikan semua environment variables sudah diset di production:

```bash
# Vercel/Netlify
vercel env add DB_HOST
vercel env add DB_PASSWORD
# dst...
```

### 3. Migration di Production

```bash
# Jalankan migration di production
NODE_ENV=production npx tsx scripts/run-migration.ts
```
