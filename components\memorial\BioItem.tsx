import { MapPin } from "lucide-react";

interface BioItemProps {
  label: string;
  value: string;
}

export function BioItem({ label, value }: BioItemProps) {
  return (
    <div>
      <span className="text-memorial-400 text-xs sm:text-sm block">{label}</span>
      <span className="text-memorial-100 text-sm sm:text-base">{value}</span>
    </div>
  );
}

interface BioItemWithLocationProps {
  label: string;
  value: string;
  locationUrl: string | null;
}

export function BioItemWithLocation({ label, value, locationUrl }: BioItemWithLocationProps) {
  return (
    <div>
      <span className="text-memorial-400 text-xs sm:text-sm block">{label}</span>
      <div className="flex items-center gap-2">
        <span className="text-memorial-100 text-sm sm:text-base">{value}</span>
      </div>
      {locationUrl && (
        <a
          href={locationUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center gap-1 text-candle-500 hover:text-candle-400 transition-colors mt-1"
          title="Lihat di Google Maps"
        >
          <MapPin className="h-3 w-3 sm:h-4 sm:w-4" />
          <span className="text-xs sm:text-sm">Lihat di Maps</span>
        </a>
      )}
    </div>
  );
}
