import { notFound } from 'next/navigation';
import { memorialService } from '@/lib/services/memorialService';
import MemorialDisplay from '@/components/memorial/MemorialDisplay';
import { Metadata } from 'next';

interface MemorialPageProps {
  params: {
    slug: string;
  };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: MemorialPageProps): Promise<Metadata> {
  try {
    const memorial = await memorialService.getMemorialBySlug(params.slug);
    
    if (!memorial) {
      return {
        title: 'Memorial Tidak Ditemukan',
        description: 'Memorial yang Anda cari tidak ditemukan.',
      };
    }

    const birthYear = new Date(memorial.birthDate).getFullYear();
    const deathYear = new Date(memorial.deathDate).getFullYear();

    return {
      title: `${memorial.name} (${birthYear} - ${deathYear}) | Memorial Digital`,
      description: `Memorial digital untuk ${memorial.name}, lahir di ${memorial.birthPlace} pada ${birthYear}, wafat pada ${deathYear}. ${memorial.description ? memorial.description.substring(0, 150) + '...' : ''}`,
      keywords: [
        memorial.name,
        'memorial digital',
        'kenangan',
        memorial.birthPlace,
        memorial.location?.name || '',
        memorial.religion?.name || '',
      ].filter(Boolean),
      openGraph: {
        title: `${memorial.name} (${birthYear} - ${deathYear})`,
        description: `Memorial digital untuk ${memorial.name}`,
        type: 'profile',
        url: `/memorial/${params.slug}`,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Memorial Digital',
      description: 'Halaman memorial digital',
    };
  }
}

export default async function MemorialPage({ params }: MemorialPageProps) {
  try {
    const memorial = await memorialService.getMemorialBySlug(params.slug);
    
    if (!memorial) {
      notFound();
    }

    // Transform data to match the expected format for MemorialDisplay
    const memorialData = {
      id: memorial.id,
      slug: memorial.slug,
      name: memorial.name,
      birthPlace: memorial.birthPlace,
      birthDate: memorial.birthDate,
      deathDate: memorial.deathDate,
      description: memorial.description,
      submittedBy: memorial.submittedBy,
      evidenceName: memorial.evidenceName,
      evidenceImageUrl: memorial.evidenceImageUrl,
      createdAt: memorial.createdAt,
      updatedAt: memorial.updatedAt,
      
      // Related data
      religion: memorial.religion ? {
        id: memorial.religion.id,
        name: memorial.religion.name,
        slug: memorial.religion.slug,
      } : null,
      
      location: memorial.location ? {
        id: memorial.location.id,
        name: memorial.location.name,
        slug: memorial.location.slug,
        addressDetail: memorial.location.addressDetail,
        province: memorial.location.province,
        city: memorial.location.city,
        district: memorial.location.district,
        subDistrict: memorial.location.subDistrict,
        latitude: memorial.location.latitude,
        longitude: memorial.location.longitude,
        placeId: memorial.location.placeId,
      } : null,
      
      images: memorial.memorialImages?.map(img => ({
        id: img.id,
        imageUrl: img.imageUrl,
        caption: img.caption,
        createdAt: img.createdAt,
      })) || [],
    };

    return (
      <main className="min-h-screen bg-memorial-950">
        <div className="container mx-auto py-8 px-4">
          <MemorialDisplay memorial={memorialData} />
        </div>
      </main>
    );
    
  } catch (error) {
    console.error('Error loading memorial:', error);
    notFound();
  }
}

// Generate static params for static generation (optional)
export async function generateStaticParams() {
  try {
    // For now, return empty array to generate pages on-demand
    // In production, you might want to pre-generate popular memorials
    return [];
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}
