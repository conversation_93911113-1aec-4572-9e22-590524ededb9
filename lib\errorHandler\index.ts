// Unified Error Handling System
import { NextResponse } from 'next/server';
import { toast } from "@/hooks/useToast";
import { ApiError } from '@/utils/ApiError';

// Error types
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export interface ErrorLogEntry {
  id: string;
  level: 'error' | 'warning' | 'info';
  message: string;
  context?: ErrorContext;
  stack?: string;
  timestamp: Date;
}

// Unified Error Handler Class
export class UnifiedErrorHandler {
  private static instance: UnifiedErrorHandler;
  private errorLogs: ErrorLogEntry[] = [];

  static getInstance(): UnifiedErrorHandler {
    if (!UnifiedErrorHandler.instance) {
      UnifiedErrorHandler.instance = new UnifiedErrorHandler();
    }
    return UnifiedErrorHandler.instance;
  }

  // Client-side error handling
  handleClientError(error: any, context?: ErrorContext): void {
    const errorEntry = this.createErrorEntry(error, context);
    this.logError(errorEntry);
    this.showUserNotification(errorEntry);
  }

  // Server-side error handling
  handleApiError(error: unknown): NextResponse {
    const errorEntry = this.createErrorEntry(error);
    this.logError(errorEntry);

    if (error instanceof ApiError) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
          code: error.code
        },
        { status: error.statusCode }
      );
    }

    // Handle validation errors
    if (error instanceof Error && error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
          code: 'VALIDATION_ERROR'
        },
        { status: 400 }
      );
    }

    // Default error response
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        code: 'INTERNAL_SERVER_ERROR'
      },
      { status: 500 }
    );
  }

  // Success notifications
  handleSuccess(message: string, title?: string): void {
    toast({
      variant: "success",
      title: title || "Berhasil",
      description: message,
    });
  }

  // Warning notifications
  handleWarning(message: string, title?: string): void {
    toast({
      variant: "warning",
      title: title || "Peringatan",
      description: message,
    });
  }

  // Info notifications
  handleInfo(message: string, title?: string): void {
    toast({
      variant: "default",
      title: title || "Informasi",
      description: message,
    });
  }

  // Private methods
  private createErrorEntry(error: any, context?: ErrorContext): ErrorLogEntry {
    return {
      id: crypto.randomUUID(),
      level: 'error',
      message: this.extractErrorMessage(error),
      context,
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date(),
    };
  }

  private extractErrorMessage(error: any): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    if (error?.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  private logError(errorEntry: ErrorLogEntry): void {
    console.error(`❌ Error in ${errorEntry.context?.component || 'Unknown'}:`, {
      message: errorEntry.message,
      context: errorEntry.context,
      timestamp: errorEntry.timestamp,
    });

    // Store in memory (in production, send to logging service)
    this.errorLogs.push(errorEntry);

    // Keep only last 100 errors in memory
    if (this.errorLogs.length > 100) {
      this.errorLogs = this.errorLogs.slice(-100);
    }
  }

  private showUserNotification(errorEntry: ErrorLogEntry): void {
    let errorTitle = "Error";
    let errorMessage = errorEntry.message;

    // Customize based on error type
    if (errorEntry.context?.action) {
      switch (errorEntry.context.action) {
        case 'fetch':
          errorTitle = "Gagal Memuat Data";
          errorMessage = "Tidak dapat memuat data. Periksa koneksi internet Anda.";
          break;
        case 'save':
          errorTitle = "Gagal Menyimpan";
          errorMessage = "Data tidak dapat disimpan. Silakan coba lagi.";
          break;
        case 'upload':
          errorTitle = "Gagal Upload";
          errorMessage = "File tidak dapat diupload. Periksa ukuran dan format file.";
          break;
        default:
          errorTitle = "Terjadi Kesalahan";
      }
    }

    toast({
      variant: "destructive",
      title: errorTitle,
      description: errorMessage,
    });
  }

  // Get error logs (for debugging)
  getErrorLogs(): ErrorLogEntry[] {
    return [...this.errorLogs];
  }

  // Clear error logs
  clearErrorLogs(): void {
    this.errorLogs = [];
  }
}

// Convenience functions
export const errorHandler = UnifiedErrorHandler.getInstance();

// Async wrapper with error handling
export async function withErrorHandling<T>(
  asyncFn: () => Promise<T>,
  context?: ErrorContext,
  showSuccessMessage?: string
): Promise<T | null> {
  try {
    const result = await asyncFn();

    if (showSuccessMessage) {
      errorHandler.handleSuccess(showSuccessMessage);
    }

    return result;
  } catch (error) {
    errorHandler.handleClientError(error, context);
    return null;
  }
}

// API request wrapper
export async function apiRequest<T>(
  url: string,
  options?: RequestInit,
  context?: ErrorContext
): Promise<T | null> {
  return withErrorHandling(async () => {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw {
        status: response.status,
        message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        details: errorData,
      };
    }

    return response.json();
  }, context || { action: 'fetch', component: `API Request to ${url}` });
}

// Legacy compatibility exports
export const handleApiError = errorHandler.handleApiError.bind(errorHandler);
export const ErrorHandler = {
  handle: errorHandler.handleClientError.bind(errorHandler),
  handleSuccess: errorHandler.handleSuccess.bind(errorHandler),
  handleWarning: errorHandler.handleWarning.bind(errorHandler),
  handleInfo: errorHandler.handleInfo.bind(errorHandler),
};
