"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Select } from "@/components/ui/select";
import { MapPin } from "lucide-react";
import { Loader } from "@googlemaps/js-api-loader";
import { ErrorHandler, apiRequest } from "@/lib/errorHandler";
import { useProvinces, useRegencies, useDistricts, useVillages, useCompleteAddress } from "@/hooks/useRegions";
import { LocationSearchResult, LocationFormData } from "@/types/location";

// Add Google Maps type definitions
declare global {
  interface Window {
    google: {
      maps: {
        Map: any;
        MapOptions: any;
        Marker: any;
        MapTypeId: {
          ROADMAP: any;
        };
        Animation: {
          DROP: any;
        };
        Geocoder: any;
        GeocoderResult: any;
      }
    };
  }
}

interface LocationSearchProps {
  initialData?: LocationSearchResult;
  onLocationSelected?: (location: LocationSearchResult) => void;
  onReset?: () => void;
}

export default function LocationSearch({ initialData, onLocationSelected, onReset }: LocationSearchProps) {
  const [formData, setFormData] = useState<LocationFormData>({
    provinsi: initialData?.provinsi_id || initialData?.provinsi || "",
    kabupaten_kota: initialData?.kabupaten_kota_id || initialData?.kabupaten_kota || "",
    kecamatan: initialData?.kecamatan_id || initialData?.kecamatan || "",
    kelurahan: initialData?.kelurahan_id || initialData?.kelurahan || "",
    alamat_detail: initialData?.alamat_detail || ""
  });

  // Reset form when initialData changes (e.g., when store is reset)
  useEffect(() => {
    setFormData({
      provinsi: initialData?.provinsi_id || initialData?.provinsi || "",
      kabupaten_kota: initialData?.kabupaten_kota_id || initialData?.kabupaten_kota || "",
      kecamatan: initialData?.kecamatan_id || initialData?.kecamatan || "",
      kelurahan: initialData?.kelurahan_id || initialData?.kelurahan || "",
      alamat_detail: initialData?.alamat_detail || ""
    });

    // Reset result when initialData is null (reset)
    if (!initialData) {
      setResult(null);
      setError(null);
    }
  }, [initialData, JSON.stringify(initialData)]);
  
  const [result, setResult] = useState<LocationSearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use region hooks
  const { provinces } = useProvinces();
  const { regencies } = useRegencies(formData.provinsi);
  const { districts } = useDistricts(formData.kabupaten_kota);
  const { villages } = useVillages(formData.kecamatan);
  const { buildAddress } = useCompleteAddress();
  
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<Window['google']['maps']['Map'] | null>(null);
  const markerRef = useRef<Window['google']['maps']['Marker'] | null>(null);

  // Auto-load map if initialData contains location coordinates
  useEffect(() => {
    if (initialData && initialData.lokasi && initialData.lokasi.latitude && initialData.lokasi.longitude) {
      // Set result to show the map immediately
      setResult(initialData);
    }
  }, [initialData]);
  
  // Initialize Google Maps when result is available
  useEffect(() => {
    if (result && mapRef.current) {
      const initMap = async () => {
        try {
          const loader = new Loader({
            apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
            version: "weekly",
            libraries: ["marker"]
          });
          
          const google = await loader.load();
          const { latitude, longitude } = result.lokasi;
          const location = { lat: parseFloat(latitude), lng: parseFloat(longitude) };
          
          const mapOptions: typeof google.maps.MapOptions = {
            center: location,
            zoom: 15,
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            mapTypeControl: true,
            streetViewControl: true,
            fullscreenControl: true,
            styles: [
              {
                featureType: "all",
                elementType: "all",
                stylers: [{ saturation: -100 }]
              }
            ]
          };
          
          googleMapRef.current = new google.maps.Map(mapRef.current, mapOptions);
          
          markerRef.current = new google.maps.Marker({
            position: location,
            map: googleMapRef.current,
            title: result.formatted_address,
            draggable: true
          });
          
          // Update coordinates when marker is dragged
          markerRef.current.addListener("dragend", () => {
            if (markerRef.current) {
              const position = markerRef.current.getPosition();
              if (position) {
                const newLat = position.lat();
                const newLng = position.lng();
                
                // Update result with new coordinates
                setResult({
                  ...result,
                  place_id: result.place_id,
                  lokasi: {
                    latitude: newLat.toString(),
                    longitude: newLng.toString()
                  }
                });
               
                
                // Get address for new coordinates
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode({ location: { lat: newLat, lng: newLng } }, 
                  (results: Window['google']['maps']['GeocoderResult'][] | null, status: string) => {
                    if (status === "OK" && results && results[0]) {
                      setResult((prev) => prev ? ({
                        ...prev,
                        formatted_address: results[0].formatted_address,
                        place_id: results[0].place_id
                      }) : null);
                    }
                  });
              }
            }
          });
          
        } catch (err) {
          console.error("Error loading Google Maps:", err);
          ErrorHandler.handle(err, "Google Maps Loading");
          setError("Gagal memuat peta Google Maps");
        }
      };
      
      initMap();
    }
  }, [result]);
  
  const handleChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value,
      // Reset dependent fields
      ...(name === 'provinsi' && { kabupaten_kota: '', kecamatan: '', kelurahan: '' }),
      ...(name === 'kabupaten_kota' && { kecamatan: '', kelurahan: '' }),
      ...(name === 'kecamatan' && { kelurahan: '' }),
    }));

  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Custom validation with specific error messages
      if (!formData.provinsi) {
        ErrorHandler.handle("Provinsi harus dipilih", "Form Validation");
        setLoading(false);
        return;
      }

      if (!formData.kabupaten_kota) {
        ErrorHandler.handle("Kabupaten/Kota harus dipilih", "Form Validation");
        setLoading(false);
        return;
      }

      if (!formData.kecamatan) {
        ErrorHandler.handle("Kecamatan harus dipilih", "Form Validation");
        setLoading(false);
        return;
      }

      if (!formData.kelurahan) {
        ErrorHandler.handle("Kelurahan harus dipilih", "Form Validation");
        setLoading(false);
        return;
      }

      // Build complete address using hook
      const addressData = await buildAddress({
        provinceId: formData.provinsi,
        regencyId: formData.kabupaten_kota,
        districtId: formData.kecamatan,
        villageId: formData.kelurahan,
        detailAddress: formData.alamat_detail,
      });

      if (!addressData) {
        setError("Gagal membangun alamat lengkap");
        setLoading(false);
        return;
      }

      const data = await apiRequest<{data: any}>(
        '/api/location',
        {
          method: 'POST',
          body: JSON.stringify(addressData),
        },
        "Location Search"
      );

      if (data?.data) {
        setResult(data.data);
        ErrorHandler.handleSuccess("Lokasi berhasil ditemukan!");
      } else {
        setError("Gagal mencari lokasi");
      }
    } catch (error) {
      ErrorHandler.handle(error, "Location Search");
      setError("Gagal mencari lokasi");
    } finally {
      setLoading(false);
    }
  };
  
  const handleSelectLocation = () => {
    if (result && onLocationSelected) {
      // Include the form IDs along with the result data
      const locationData = {
        ...result,
        // Store the IDs for future reference
        provinsi_id: formData.provinsi,
        kabupaten_kota_id: formData.kabupaten_kota,
        kecamatan_id: formData.kecamatan,
        kelurahan_id: formData.kelurahan,
        // Keep the place_id from the result
        place_id: result.place_id
      };
      onLocationSelected(locationData);
    }
  };


  
  return (
    <div className="w-full max-w-md mx-auto">
      <h2 className="text-xl font-semibold mb-6 text-center">Pilih Lokasi Pemakaman</h2>
      <div className="max-w-md mx-auto p-6 bg-memorial-900 rounded-lg border border-memorial-800">
        <h2 className="text-xl font-bold mb-4 text-memorial-50">Cari Koordinat Lokasi</h2>

        {/* Show notice if location is already loaded */}
        {result && initialData && initialData.lokasi && (
          <div className="mb-4 p-3 bg-green-900/30 border border-green-800 rounded-lg">
            <p className="text-sm text-green-200">
              ✅ <strong>Lokasi sudah dipilih sebelumnya.</strong> Anda dapat mengubah koordinat dengan drag marker atau cari lokasi baru.
            </p>
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm text-memorial-300 mb-1">Provinsi</label>
            <Select
              name="provinsi"
              value={formData.provinsi}
              onChange={(value) => handleChange('provinsi', value)}
              options={provinces.map(p => ({ value: p.id, label: p.name }))}
              placeholder="Pilih Provinsi"
              className="bg-memorial-800 border-memorial-700 text-memorial-50"
            />
          </div>
          
          <div>
            <label className="block text-sm text-memorial-300 mb-1">Kabupaten/Kota</label>
            <Select
              name="kabupaten_kota"
              value={formData.kabupaten_kota}
              onChange={(value) => handleChange('kabupaten_kota', value)}
              options={regencies.map(r => ({ value: r.id, label: r.name }))}
              placeholder="Pilih Kabupaten/Kota"
              className="bg-memorial-800 border-memorial-700 text-memorial-50"
              disabled={!formData.provinsi}
            />
          </div>
          
          <div>
            <label className="block text-sm text-memorial-300 mb-1">Kecamatan</label>
            <Select
              name="kecamatan"
              value={formData.kecamatan}
              onChange={(value) => handleChange('kecamatan', value)}
              options={districts.map(d => ({ value: d.id, label: d.name }))}
              placeholder="Pilih Kecamatan"
              className="bg-memorial-800 border-memorial-700 text-memorial-50"
              disabled={!formData.kabupaten_kota}
            />
          </div>
          
          <div>
            <label className="block text-sm text-memorial-300 mb-1">Kelurahan</label>
            <Select
              name="kelurahan"
              value={formData.kelurahan}
              onChange={(value) => handleChange('kelurahan', value)}
              options={villages.map(v => ({ value: v.id, label: v.name }))}
              placeholder="Pilih Kelurahan"
              className="bg-memorial-800 border-memorial-700 text-memorial-50"
              disabled={!formData.kecamatan}
            />
          </div>
          
          <div>
            <label className="block text-sm text-memorial-300 mb-1">Alamat Detail</label>
            <input
              type="text"
              name="alamat_detail"
              value={formData.alamat_detail}
              onChange={(e) => handleChange('alamat_detail', e.target.value)}
              placeholder="contoh: Jl. KH Achmad Dahlan No. 123"
              className="w-full p-2 rounded bg-memorial-800 border border-memorial-700 text-memorial-50 placeholder:text-memorial-500"
            />
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            {onReset && (
              <Button
                type="button"
                variant="outline"
                onClick={onReset}
                className="w-full sm:w-auto border-memorial-600 text-memorial-300 hover:bg-memorial-800 hover:text-memorial-50"
              >
                Reset Form
              </Button>
            )}
            <Button
              type="submit"
              className="flex-1 bg-candle-500 text-memorial-950 hover:bg-candle-400"
              disabled={loading}
            >
              {loading ? "Mencari..." : "Cari Koordinat"}
            </Button>
          </div>
        </form>
        
        {error && (
          <div className="mt-4 p-3 bg-red-900/50 border border-red-800 rounded text-red-200">
            {error}
          </div>
        )}
        
        {result && (
          <div className="mt-6 p-4 bg-memorial-800 rounded-lg border border-memorial-700">
            <h3 className="font-medium text-memorial-50 mb-2">Hasil Pencarian:</h3>
            
            <div className="flex items-start gap-2 mb-3">
              <MapPin className="h-5 w-5 text-candle-500 mt-0.5 flex-shrink-0" />
              <div>
                {result.formatted_address && (
                  <p className="text-sm text-memorial-400">{result.formatted_address}</p>
                )}
              </div>
            </div>
            <p className="text-center">drag <span className="font-bold text-candle-500">marker</span> untuk mengubah koordinat</p>
            
            {/* Google Map */}
            <div 
              ref={mapRef} 
              className="w-full h-64 rounded-lg mb-3 bg-memorial-700"
            ></div>
            
            
            <div className="mt-4 flex justify-between">
              {result.place_id && (
                <a 
                  href={`https://www.google.com/maps/place/?q=place_id:${result.place_id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-candle-500 hover:text-candle-400 underline"
                >
                  Lihat di Google Maps
                </a>
              )}
              <Button
                onClick={handleSelectLocation}
                className="bg-candle-500 text-memorial-950 hover:bg-candle-400"
              >
                Pilih Lokasi
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}



