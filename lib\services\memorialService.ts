import { db } from '@/db';
import { memorials, locations, religions, memorialImages } from '@/db/schema';
import { eq, desc, sql } from 'drizzle-orm';
import { ApiError } from '@/utils/ApiError';
import { BaseService, PaginationOptions, PaginatedResponse, handleServiceError } from '@/lib/services/base/BaseService';
import { MemorialApiResponse, PaginationInfo } from '@/types';

export class MemorialService extends BaseService {
  constructor() {
    super('MemorialService', {
      enableCache: true,
      cacheTimeout: 5 * 60 * 1000, // 5 minutes
      enableLogging: true,
    });
  }
  /**
   * Get memorial by ID with related data
   */
  async getById(id: string) {
    try {
      this.validateId(id, 'Memorial ID');

      const memorial = await db.query.memorials.findFirst({
        where: (memorials, { eq }) => eq(memorials.id, id),
        with: {
          memorialImages: true,
          location: true,
          religion: true,
        }
      });

      if (!memorial) {
        throw new ApiError('Memorial not found', 404, 'MEMORIAL_NOT_FOUND');
      }

      return memorial;
    } catch (error) {
      handleServiceError(error, 'getMemorialById', this.serviceName);
    }
  }

  // Legacy method for backward compatibility
  async getMemorialById(id: string) {
    return this.getById(id);
  }

  /**
   * Get memorial by slug with related data
   */
  async getMemorialBySlug(slug: string) {
    try {
      const memorial = await db.query.memorials.findFirst({
        where: (memorials, { eq }) => eq(memorials.slug, slug),
        with: {
          memorialImages: true,
          location: true,
          religion: true,
          orders: true, // Include orders to get package info
        }
      });

      if (!memorial) {
        return null; // Return null instead of throwing error for slug lookup
      }

      // Add package info from the latest order
      const latestOrder = memorial.orders?.[0]; // Assuming orders are ordered by creation date
      const memorialWithPackage = {
        ...memorial,
        packageId: latestOrder?.packageId || 'free',
        packageName: latestOrder?.packageName || 'Paket Free',
      };

      return memorialWithPackage;

    } catch (error) {
      console.error('Get memorial by slug error:', error);
      throw new ApiError(
        'Failed to retrieve memorial by slug',
        500,
        'MEMORIAL_SLUG_RETRIEVAL_ERROR'
      );
    }
  }

  /**
   * Get all memorials with pagination and filters
   */
  async getAll(options: PaginationOptions & { search?: string } = { page: 1, limit: 10 }) {
    this.validatePagination(options);

    return this.executeOperation(
      async () => {
        const { page, limit, search } = options;
        const offset = (page - 1) * limit;

        // Get total count for pagination
        const [totalCountResult] = await db
          .select({ count: sql<number>`count(*)` })
          .from(memorials);

        const totalItems = totalCountResult.count;
        const totalPages = Math.ceil(totalItems / limit);

        // Build main query
        let query = db
          .select({
            id: memorials.id,
            slug: memorials.slug,
            name: memorials.name,
            birthPlace: memorials.birthPlace,
            birthDate: memorials.birthDate,
            deathDate: memorials.deathDate,
            description: memorials.description,
            submittedBy: memorials.submittedBy,
            evidenceName: memorials.evidenceName,
            evidenceImageUrl: memorials.evidenceImageUrl,
            createdAt: memorials.createdAt,
            updatedAt: memorials.updatedAt,
            // Location data
            locationName: locations.name,
            locationAddress: locations.addressDetail,
            locationProvince: locations.province,
            locationCity: locations.city,
            locationDistrict: locations.district,
            locationSubDistrict: locations.subDistrict,
            locationLatitude: locations.latitude,
            locationLongitude: locations.longitude,
            locationPlaceId: locations.placeId,
            // Religion data
            religionName: religions.name,
          })
          .from(memorials)
          .leftJoin(locations, eq(memorials.locationId, locations.id))
          .leftJoin(religions, eq(memorials.religionId, religions.id))
          .orderBy(desc(memorials.createdAt))
          .limit(limit)
          .offset(offset);

        const results = await query;

        // Get images for each memorial
        const memorialsWithImages = await Promise.all(
          results.map(async (memorial) => {
            const images = await db
              .select({
                id: memorialImages.id,
                url: memorialImages.imageUrl,
                caption: memorialImages.caption,
              })
              .from(memorialImages)
              .where(eq(memorialImages.memorialId, memorial.id));

            return {
              ...memorial,
              images,
              birthYear: new Date(memorial.birthDate).getFullYear(),
              deathYear: new Date(memorial.deathDate).getFullYear(),
              cemeteryName: memorial.locationName || 'Unknown Cemetery',
              location: {
                name: memorial.locationName,
                address: memorial.locationAddress,
                province: memorial.locationProvince,
                city: memorial.locationCity,
                district: memorial.locationDistrict,
                subDistrict: memorial.locationSubDistrict,
                latitude: memorial.locationLatitude,
                longitude: memorial.locationLongitude,
                place_id: memorial.locationPlaceId,
              },
            };
          })
        );

        const pagination: PaginationInfo = {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          nextPage: page < totalPages ? page + 1 : null,
          prevPage: page > 1 ? page - 1 : null,
        };

        return this.createPaginatedResponse(memorialsWithImages, pagination);
      },
      'getAllMemorials',
      `memorials:page:${options.page}:limit:${options.limit}${options.search ? `:search:${options.search}` : ''}`
    );
  }

  // Legacy method for backward compatibility
  async getAllMemorials(options?: {
    page?: number;
    limit?: number;
    search?: string;
  }) {
    return this.getAll({
      page: options?.page || 1,
      limit: options?.limit || 10,
      search: options?.search,
    });
  }

  /**
   * Search memorials by name or other criteria
   */
  async searchMemorials(searchTerm: string, options?: {
    page?: number;
    limit?: number;
  }) {
    try {
      const page = options?.page || 1;
      const limit = options?.limit || 10;
      const offset = (page - 1) * limit;

      // Simple search implementation - can be enhanced with full-text search
      const results = await db
        .select({
          id: memorials.id,
          slug: memorials.slug,
          name: memorials.name,
          birthPlace: memorials.birthPlace,
          birthDate: memorials.birthDate,
          deathDate: memorials.deathDate,
          description: memorials.description,
          submittedBy: memorials.submittedBy,
          createdAt: memorials.createdAt,
          locationName: locations.name,
          religionName: religions.name,
        })
        .from(memorials)
        .leftJoin(locations, eq(memorials.locationId, locations.id))
        .leftJoin(religions, eq(memorials.religionId, religions.id))
        .where(
          // Search in name, birth place, or description
          // Note: This is a simple implementation, consider using full-text search for production
          eq(memorials.name, searchTerm) // Simplified for now
        )
        .limit(limit)
        .offset(offset)
        .orderBy(memorials.createdAt);

      return {
        data: results,
        pagination: {
          page,
          limit,
          total: results.length,
          hasMore: results.length === limit
        }
      };

    } catch (error) {
      console.error('Search memorials error:', error);
      throw new ApiError(
        'Failed to search memorials',
        500,
        'MEMORIAL_SEARCH_ERROR'
      );
    }
  }
}

export const memorialService = new MemorialService();
