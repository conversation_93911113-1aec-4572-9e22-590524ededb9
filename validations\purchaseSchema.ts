import { z } from 'zod';

// Location data validation schema
const locationDataSchema = z.object({
  name: z.string().min(1, "Nama lokasi tidak boleh kosong"),
  addressDetail: z.string().min(1, "Detail alamat tidak boleh kosong"),
  province: z.string().min(1, "Provinsi tidak boleh kosong"),
  city: z.string().min(1, "Kota/Kabupaten tidak boleh kosong"),
  district: z.string().min(1, "Kecamatan tidak boleh kosong"),
  subDistrict: z.string().min(1, "Kelurahan tidak boleh kosong"),
  // Accept both string and number for coordinates, then convert to string
  latitude: z.union([z.string(), z.number()]).transform((val) => String(val)),
  longitude: z.union([z.string(), z.number()]).transform((val) => String(val)),
  placeId: z.string().optional(),
});

// Package details validation schema
const packageDetailsSchema = z.object({
  id: z.string().min(1, "Package ID tidak boleh kosong"),
  name: z.string().min(1, "Nama paket tidak boleh kosong"),
  price: z.coerce.number().min(0, "Harga tidak boleh negatif"),
  duration: z.string().min(1, "Durasi paket tidak boleh kosong"),
  features: z.array(z.string()).min(1, "Fitur paket tidak boleh kosong"),
});

// Base64 image validation
const base64ImageSchema = z.string().refine(
  (value) => {
    // Check if it's a valid base64 data URL for images
    const base64Regex = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/;
    return base64Regex.test(value);
  },
  {
    message: "Invalid base64 image format. Must be a valid data URL with image type.",
  }
);

// Optional base64 image schema that accepts null
const optionalBase64ImageSchema = z.union([base64ImageSchema, z.null()]).optional();

// Base purchase schema without refinements
const basePurchaseSchema = z.object({
  // Bio data fields
  name: z.string().min(1, "Nama tidak boleh kosong").max(255, "Nama terlalu panjang"),
  birthPlace: z.string().min(1, "Tempat lahir tidak boleh kosong").max(255, "Tempat lahir terlalu panjang"),
  birthDate: z.string().min(1, "Tanggal lahir tidak boleh kosong").refine(
    (date) => {
      const parsedDate = new Date(date);
      return !isNaN(parsedDate.getTime()) && parsedDate < new Date();
    },
    {
      message: "Tanggal lahir harus valid dan tidak boleh di masa depan",
    }
  ),
  deathDate: z.string().min(1, "Tanggal meninggal tidak boleh kosong").refine(
    (date) => {
      const parsedDate = new Date(date);
      return !isNaN(parsedDate.getTime()) && parsedDate <= new Date();
    },
    {
      message: "Tanggal meninggal harus valid dan tidak boleh di masa depan",
    }
  ),
  religionId: z.union([
    z.string().min(1, "Religion ID tidak boleh kosong"),
    z.null()
  ]).optional(),
  submittedBy: z.string().min(1, "Nama pengaju tidak boleh kosong").max(255, "Nama pengaju terlalu panjang"),
  description: z.string().min(1, "Kisah hidup tidak boleh kosong"),
  evidenceName: z.string().min(1, "Nama bukti kematian tidak boleh kosong").max(255, "Nama bukti kematian terlalu panjang"),

  // Location data
  locationData: locationDataSchema,

  // Package details
  packageDetails: packageDetailsSchema,

  // Payment authorization token (required for upload authorization)
  paymentToken: z.string().min(1, "Payment token required"),

  // Optional file uploads (base64 encoded)
  images: z.array(base64ImageSchema).max(2, "Maksimal 2 foto").optional(),
  evidenceImage: optionalBase64ImageSchema,
});

// Main purchase validation schema with cross-field validation
export const purchaseSchema = basePurchaseSchema.refine(
  (data) => {
    // Cross-field validation: birth date must be before death date
    const birthDate = new Date(data.birthDate);
    const deathDate = new Date(data.deathDate);
    return birthDate < deathDate;
  },
  {
    message: "Tanggal lahir harus sebelum tanggal meninggal",
    path: ["birthDate"], // This will attach the error to the birthDate field
  }
).refine(
  (data) => {
    // Validate image count based on package
    if (data.packageDetails.id === 'free') {
      return !data.images || data.images.length <= 1;
    }
    return !data.images || data.images.length <= 2;
  },
  {
    message: "Jumlah foto melebihi batas paket yang dipilih",
    path: ["images"],
  }
);

// Type inference
export type PurchaseRequest = z.infer<typeof purchaseSchema>;

// Validation function
export const validatePurchaseRequest = (data: unknown): PurchaseRequest => {
  const result = purchaseSchema.safeParse(data);
  
  if (!result.success) {
    const errorMessages = result.error.errors.map(err => 
      `${err.path.join('.')}: ${err.message}`
    ).join(', ');
    
    throw new Error(`Validation failed: ${errorMessages}`);
  }
  
  return result.data;
};

// Helper function to validate individual steps
export const validateBioData = (data: unknown) => {
  const bioDataSchema = basePurchaseSchema.pick({
    name: true,
    birthPlace: true,
    birthDate: true,
    deathDate: true,
    religionId: true,
    submittedBy: true,
    description: true,
    evidenceName: true,
  });

  return bioDataSchema.parse(data);
};

export const validateLocationData = (data: unknown) => {
  return locationDataSchema.parse(data);
};

export const validatePackageDetails = (data: unknown) => {
  return packageDetailsSchema.parse(data);
};
