import { supabase } from '@/lib/supabase';
import { getSupabaseClient, hasPaymentAuthorization } from '@/lib/supabaseServiceRole';
import { ApiError } from '@/utils/ApiError';

export interface UploadResult {
  url: string;
  path: string;
  publicUrl: string;
}

export class ImageStorageService {
  private readonly BUCKET_NAME = 'memorial-images';
  private readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private readonly ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  /**
   * Upload base64 image to Supabase Storage
   */
  async uploadBase64Image(
    base64Data: string,
    fileName: string,
    folder: 'memorial' | 'evidence' = 'memorial',
    paymentToken?: string
  ): Promise<UploadResult> {
    try {
      // Get appropriate Supabase client based on payment authorization
      const supabaseClient = getSupabaseClient(paymentToken);

      // For environments without Supabase configured, return placeholder URLs
      if (!supabaseClient) {
        console.log('📸 Supabase not configured or no payment authorization: Using placeholder image URLs');
        return this.createLocalPlaceholder(fileName, folder);
      }

      // Log payment authorization status
      if (hasPaymentAuthorization(paymentToken)) {
        console.log('✅ Payment authorized: Using service role for upload');
      } else {
        console.log('⚠️ No payment authorization: Upload may fail due to RLS');
      }

      // Validate base64 format
      if (!this.isValidBase64Image(base64Data)) {
        throw new ApiError('Invalid base64 image format', 400, 'INVALID_IMAGE_FORMAT');
      }

      // Extract file info from base64
      const { buffer, mimeType } = this.parseBase64(base64Data);

      // Validate file size
      if (buffer.length > this.MAX_FILE_SIZE) {
        throw new ApiError('File size too large. Maximum 5MB allowed.', 400, 'FILE_TOO_LARGE');
      }

      // Validate file type
      if (!this.ALLOWED_TYPES.includes(mimeType)) {
        throw new ApiError('Invalid file type. Only JPEG, PNG, and WebP are allowed.', 400, 'INVALID_FILE_TYPE');
      }

      // Generate unique file path
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const extension = this.getExtensionFromMimeType(mimeType);
      const filePath = `${folder}/${timestamp}_${randomString}_${fileName}.${extension}`;

      try {
        // Upload to Supabase Storage
        const { data, error } = await supabaseClient.storage
          .from(this.BUCKET_NAME)
          .upload(filePath, buffer, {
            contentType: mimeType,
            cacheControl: '3600',
            upsert: false
          });

        if (error) {
          console.error('Supabase upload error:', error);

          // Handle common Supabase errors with fallback to placeholder
          if (
            error.message?.includes('Bucket not found') ||
            error.message?.includes('404') ||
            error.message?.includes('row-level security policy') ||
            error.message?.includes('Unauthorized') ||
            error.message?.includes('403')
          ) {
            console.warn(`Supabase error (${error.message}), falling back to placeholder`);
            return this.createLocalPlaceholder(fileName, folder);
          }

          throw new ApiError(`Failed to upload image to storage: ${error.message}`, 500, 'UPLOAD_FAILED');
        }

        // Get public URL
        const { data: publicUrlData } = supabaseClient.storage
          .from(this.BUCKET_NAME)
          .getPublicUrl(filePath);

        return {
          url: data.path,
          path: filePath,
          publicUrl: publicUrlData.publicUrl
        };

      } catch (supabaseError: any) {
        console.error('Supabase operation failed:', supabaseError);

        // Fallback to placeholder if Supabase fails
        console.warn('Supabase operation failed, falling back to placeholder');
        return this.createLocalPlaceholder(fileName, folder);
      }

    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('Image upload error:', error);
      throw new ApiError('Failed to upload image', 500, 'UPLOAD_ERROR');
    }
  }

  /**
   * Upload multiple base64 images
   */
  async uploadMultipleBase64Images(
    base64Images: string[],
    baseFileName: string,
    folder: 'memorial' | 'evidence' = 'memorial',
    paymentToken?: string
  ): Promise<UploadResult[]> {
    try {
      // Check payment authorization for multiple uploads
      const supabaseClient = getSupabaseClient(paymentToken);

      // For environments without Supabase configured, return placeholder URLs
      if (!supabaseClient) {
        console.log(`📸 Supabase not configured or no payment authorization: Creating ${base64Images.length} placeholder image URLs`);
        return base64Images.map((_, index) =>
          this.createLocalPlaceholder(`${baseFileName}_${index + 1}`, folder)
        );
      }

      const uploadPromises = base64Images.map((base64, index) =>
        this.uploadBase64Image(base64, `${baseFileName}_${index + 1}`, folder, paymentToken)
      );

      const results = await Promise.all(uploadPromises);
      return results;

    } catch (error) {
      console.error('Multiple image upload error:', error);
      throw new ApiError('Failed to upload multiple images', 500, 'MULTIPLE_UPLOAD_ERROR');
    }
  }

  /**
   * Delete image from storage
   */
  async deleteImage(filePath: string): Promise<void> {
    try {
      if (!supabase) {
        console.log(`🗑️ Local development: Skipping delete for ${filePath}`);
        return; // Skip deletion in local development
      }

      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        console.error('Supabase delete error:', error);
        throw new ApiError('Failed to delete image from storage', 500, 'DELETE_FAILED');
      }

    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('Image delete error:', error);
      throw new ApiError('Failed to delete image', 500, 'DELETE_ERROR');
    }
  }

  /**
   * Delete multiple images from storage
   */
  async deleteMultipleImages(filePaths: string[]): Promise<void> {
    try {
      if (!supabase) {
        console.log(`🗑️ Local development: Skipping delete for ${filePaths.length} files`);
        return; // Skip deletion in local development
      }

      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove(filePaths);

      if (error) {
        console.error('Supabase multiple delete error:', error);
        throw new ApiError('Failed to delete images from storage', 500, 'MULTIPLE_DELETE_FAILED');
      }

    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('Multiple image delete error:', error);
      throw new ApiError('Failed to delete multiple images', 500, 'MULTIPLE_DELETE_ERROR');
    }
  }

  /**
   * Validate base64 image format
   */
  private isValidBase64Image(base64Data: string): boolean {
    const base64Regex = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/;
    return base64Regex.test(base64Data);
  }

  /**
   * Parse base64 data to buffer and mime type
   */
  private parseBase64(base64Data: string): { buffer: Buffer; mimeType: string } {
    try {
      // Extract mime type
      const mimeMatch = base64Data.match(/^data:([^;]+);base64,/);
      if (!mimeMatch) {
        throw new Error('Invalid base64 format');
      }

      const mimeType = mimeMatch[1];
      
      // Extract base64 content
      const base64Content = base64Data.replace(/^data:[^;]+;base64,/, '');
      
      // Convert to buffer
      const buffer = Buffer.from(base64Content, 'base64');

      return { buffer, mimeType };

    } catch (error) {
      throw new ApiError('Failed to parse base64 data', 400, 'PARSE_ERROR');
    }
  }

  /**
   * Get file extension from mime type
   */
  private getExtensionFromMimeType(mimeType: string): string {
    const extensions: Record<string, string> = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp',
      'image/gif': 'gif'
    };

    return extensions[mimeType] || 'jpg';
  }

  /**
   * Get public URL for existing file
   */
  getPublicUrl(filePath: string): string {
    if (!supabase) {
      throw new ApiError('Supabase storage not configured', 500, 'STORAGE_NOT_CONFIGURED');
    }

    const { data } = supabase.storage
      .from(this.BUCKET_NAME)
      .getPublicUrl(filePath);

    return data.publicUrl;
  }

  /**
   * Check if bucket exists and create if needed
   */
  async ensureBucketExists(): Promise<void> {
    try {
      if (!supabase) {
        console.warn('Supabase storage not configured, skipping bucket check');
        return;
      }

      const { data: buckets, error: listError } = await supabase.storage.listBuckets();
      
      if (listError) {
        console.error('Error listing buckets:', listError);
        return;
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.BUCKET_NAME);
      
      if (!bucketExists) {
        const { error: createError } = await supabase.storage.createBucket(this.BUCKET_NAME, {
          public: true,
          allowedMimeTypes: this.ALLOWED_TYPES,
          fileSizeLimit: this.MAX_FILE_SIZE
        });

        if (createError) {
          console.error('Error creating bucket:', createError);
          console.warn('Bucket creation failed, uploads will fallback to placeholder URLs');
        } else {
          console.log(`Bucket ${this.BUCKET_NAME} created successfully`);
        }
      }

    } catch (error) {
      console.error('Error ensuring bucket exists:', error);
    }
  }

  /**
   * Setup bucket with proper RLS policies (for development/testing)
   */
  async setupBucketPolicies(): Promise<void> {
    try {
      if (!supabase) {
        console.warn('Supabase storage not configured, skipping policy setup');
        return;
      }

      console.log('Setting up bucket policies for development...');

      // Note: In production, these policies should be set via Supabase Dashboard
      // This is just for development/testing purposes

      // For now, we'll rely on fallback to placeholder URLs
      // In production, proper RLS policies should be configured in Supabase Dashboard:
      //
      // Policy for public read access:
      // CREATE POLICY "Public read access" ON storage.objects FOR SELECT USING (bucket_id = 'memorial-images');
      //
      // Policy for authenticated upload:
      // CREATE POLICY "Authenticated upload" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'memorial-images');

      console.log('Bucket policies setup completed (using fallback strategy)');

    } catch (error) {
      console.error('Error setting up bucket policies:', error);
    }
  }

  /**
   * Create placeholder URLs for local development
   */
  private createLocalPlaceholder(fileName: string, folder: string): UploadResult {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const placeholderPath = `${folder}/${timestamp}_${randomString}_${fileName}`;

    // Use placeholder image service or local path
    const placeholderUrl = `https://via.placeholder.com/400x300/1a1a1a/ffffff?text=${encodeURIComponent(fileName)}`;

    return {
      url: placeholderPath,
      path: placeholderPath,
      publicUrl: placeholderUrl
    };
  }
}

// Export singleton instance
export const imageStorageService = new ImageStorageService();
